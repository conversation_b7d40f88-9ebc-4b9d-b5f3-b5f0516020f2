type: specs.openrewrite.org/v1beta/recipe
name: ReplaceSlf4jLoggerWithOLogger
displayName: Replace org.slf4j.Logger with com.oocl.OLogger Singleton
description: >
  将所有 org.slf4j.Logger 类型替换为 com.oocl.OLogger 单例引用。
recipeList:
  - org.openrewrite.java.ChangeType:
      oldFullyQualifiedTypeName: org.slf4j.Logger
      newFullyQualifiedTypeName: com.oocl.OLogger
  - org.openrewrite.java.ChangeMethodTargetToStatic:
      methodPattern: org.slf4j.Logger *(..)
      targetType: com.oocl.OLogger
      targetMethodName: *(..)
  - org.openrewrite.java.ReplaceVariableWithStaticField:
      variableType: com.oocl.OLogger
      staticField: com.oocl.OLogger